# converter/gui.py
import json
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
from .core import check_ffmpeg, process_directory, _setup_logger

SETTINGS_FILE = Path.home() / ".mp3_to_wav_gui.json"

# ---------- Settings helpers -------------------------------------------------
def _load_settings() -> dict:
    try:
        return json.loads(SETTINGS_FILE.read_text())
    except Exception:
        return {"last_dir": str(Path.home()), "channels": 1, "rate": 44100}

def _save_settings(settings: dict):
    SETTINGS_FILE.write_text(json.dumps(settings, indent=2))

# ---------- GUI --------------------------------------------------------------
class App(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("MP3 ➜ WAV Converter")
        self.geometry("600x400")
        self.resizable(False, False)

        self.settings = _load_settings()
        self.logger = _setup_logger()
        self._build_ui()

    # ------------------------------------------------------------------ #
    def _build_ui(self):
        frm = ttk.Frame(self, padding=10)
        frm.pack(fill="both", expand=True)

        # Folder selection
        ttk.Label(frm, text="Folder:").grid(row=0, column=0, sticky="w")
        self.folder_var = tk.StringVar(value=self.settings["last_dir"])
        ent = ttk.Entry(frm, textvariable=self.folder_var, width=60)
        ent.grid(row=0, column=1, padx=5)
        ttk.Button(frm, text="Browse…", command=self._browse).grid(row=0, column=2)

        # Options
        ttk.Label(frm, text="Channels:").grid(row=1, column=0, sticky="w")
        self.channels_var = tk.IntVar(value=self.settings["channels"])
        ttk.Radiobutton(frm, text="Mono", variable=self.channels_var, value=1).grid(
            row=1, column=1, sticky="w")
        ttk.Radiobutton(frm, text="Stereo", variable=self.channels_var, value=2).grid(
            row=1, column=1, padx=70, sticky="w")

        ttk.Label(frm, text="Sample rate:").grid(row=2, column=0, sticky="w")
        self.rate_var = tk.IntVar(value=self.settings["rate"])
        cb = ttk.Combobox(frm, textvariable=self.rate_var,
                          values=[8000, 16000, 22050, 44100, 48000], state="readonly")
        cb.grid(row=2, column=1, sticky="w")

        # Progress
        self.pb = ttk.Progressbar(frm, orient="horizontal", mode="determinate")
        self.pb.grid(row=3, column=0, columnspan=3, pady=10, sticky="we")

        self.status = ttk.Label(frm, text="Ready")
        self.status.grid(row=4, column=0, columnspan=3)

        # Buttons
        btn_frm = ttk.Frame(frm)
        btn_frm.grid(row=5, column=0, columnspan=3, pady=20)
        ttk.Button(btn_frm, text="Start", command=self._start).pack(side="left", padx=5)
        ttk.Button(btn_frm, text="Quit", command=self.quit).pack(side="left", padx=5)

    # ------------------------------------------------------------------ #
    def _browse(self):
        path = filedialog.askdirectory(initialdir=self.settings["last_dir"])
        if path:
            self.folder_var.set(path)

    def _start(self):
        folder = Path(self.folder_var.get())
        if not folder.is_dir():
            messagebox.showerror("Error", "Please select a valid folder")
            return

        self.settings["last_dir"] = str(folder)
        self.settings["channels"] = self.channels_var.get()
        self.settings["rate"] = self.rate_var.get()
        _save_settings(self.settings)

        if not check_ffmpeg():
            messagebox.showerror("Error", "FFmpeg not found")
            return

        self.pb["value"] = 0
        self.update()
        try:
            ok, err = process_directory(
                folder,
                self.rate_var.get(),
                self.channels_var.get(),
                self.logger,
                progress_cb=self._update_progress,
            )
            messagebox.showinfo("Done", f"Success: {ok}\nErrors: {err}")
        except Exception as exc:
            messagebox.showerror("Error", str(exc))

    def _update_progress(self, current, total, ok, err):
        self.pb["maximum"] = total
        self.pb["value"] = current
        self.status.config(text=f"{current}/{total}  ok:{ok}  err:{err}")
        self.update_idletasks()


# ---------- Entry point ------------------------------------------------------
def run():
    app = App()
    app.mainloop()