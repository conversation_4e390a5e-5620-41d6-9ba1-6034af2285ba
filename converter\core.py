# converter/core.py
import os
import sys
import logging
import subprocess
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Tuple

TARGET_SAMPLE_RATE = 44_100
TARGET_BIT_DEPTH   = 16
TARGET_CHANNELS    = 1

LOGGER_NAME = "mp3_to_wav"


def _ffmpeg_cmd(in_path: Path, out_path: Path,
                sample_rate: int, channels: int) -> list[str]:
    return [
        "ffmpeg", "-y", "-v", "error",
        "-i", str(in_path),
        "-ar", str(sample_rate),
        "-ac", str(channels),
        "-acodec", "pcm_s16le",
        str(out_path)
    ]


def check_ffmpeg() -> bool:
    try:
        subprocess.run(["ffmpeg", "-version"], check=True,
                       stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def _setup_logger(log_file: str = "conversion.log") -> logging.Logger:
    log = logging.getLogger(LOGGER_NAME)
    log.setLevel(logging.INFO)
    fmt = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s",
                            datefmt="%Y-%m-%d %H:%M:%S")
    if not log.handlers:  # prevent duplicate handlers in GUI reloads
        ch = logging.StreamHandler()
        fh = logging.FileHandler(log_file, encoding="utf-8")
        for h in (ch, fh):
            h.setFormatter(fmt)
            log.addHandler(h)
    return log


def convert_file(input_path: Path,
                 sample_rate: int, channels: int,
                 logger: logging.Logger) -> bool:
    tmp = input_path.with_suffix(".wav.tmp")
    out = input_path.with_suffix(".wav")
    try:
        subprocess.run(_ffmpeg_cmd(input_path, tmp, sample_rate, channels),
                       check=True, capture_output=True, text=True)
        tmp.replace(out)
        input_path.unlink()
        logger.info(f"OK  → {out.name}")
        return True
    except Exception as exc:
        if tmp.exists():
            tmp.unlink()
        logger.error(f"ERR → {input_path.name}: {exc}")
        return False


def process_directory(root: Path,
                      sample_rate: int, channels: int,
                      logger: logging.Logger,
                      progress_cb=None) -> Tuple[int, int]:
    if not root.is_dir():
        raise ValueError(f"Not a directory: {root}")
    mp3_files = list(root.rglob("*.mp3"))
    total = len(mp3_files)
    if total == 0:
        logger.info("No MP3 files found.")
        return 0, 0

    ok = err = 0
    max_workers = min(os.cpu_count() or 2, 8)
    with ThreadPoolExecutor(max_workers=max_workers) as pool:
        futures = {
            pool.submit(convert_file, p, sample_rate, channels, logger): p
            for p in mp3_files
        }
        for i, f in enumerate(as_completed(futures)):
            ok += bool(f.result())
            err = i + 1 - ok
            if progress_cb:
                progress_cb(i + 1, total, ok, err)
    return ok, err


def cli_main():
    import argparse
    parser = argparse.ArgumentParser(description="MP3 ➜ WAV batch converter")
    parser.add_argument("directory", help="Root folder to scan")
    parser.add_argument("--rate", type=int, default=TARGET_SAMPLE_RATE)
    parser.add_argument("--channels", type=int, choices=[1, 2], default=TARGET_CHANNELS)
    args = parser.parse_args()

    if not check_ffmpeg():
        print("ERROR: FFmpeg not found in PATH", file=sys.stderr)
        sys.exit(1)

    logger = _setup_logger()
    ok, err = process_directory(Path(args.directory), args.rate, args.channels, logger)
    logger.info(f"Done: {ok} ok, {err} errors")