#!/usr/bin/env python3
"""
MP3 to WAV Converter - Robustní batch konverze
Podporuje: re<PERSON><PERSON><PERSON><PERSON><PERSON>, ch<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
"""
import argparse
import os
import sys
import logging
import subprocess
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

# Konfigurace převodu
TARGET_SAMPLE_RATE = 44100
TARGET_BIT_DEPTH = 16
TARGET_CHANNELS = 1  # 1 = mono, 2 = stereo

def setup_logger():
    """Vytvoří detailní logger pro sledování průběhu a chyb"""
    logger = logging.getLogger('mp3_to_wav')
    logger.setLevel(logging.INFO)
    
    # Formát zpráv
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Konzolový výstup
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # So<PERSON>orový výstup
    file_handler = logging.FileHandler('conversion.log', encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger

def check_ffmpeg():
    """Ověří dostupnost FFmpeg v systému"""
    try:
        subprocess.run(
            ['ffmpeg', '-version'],
            check=True,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def convert_file(input_path: Path, logger: logging.Logger):
    """Převede jeden MP3 soubor na WAV a nahradí originál"""
    output_path = input_path.with_suffix('.wav')
    temp_path = input_path.with_suffix('.wav.tmp')
    
    try:
        # Příkaz pro FFmpeg
        cmd = [
            'ffmpeg', '-y', '-v', 'error', '-i', str(input_path),
            '-ar', str(TARGET_SAMPLE_RATE),
            '-ac', str(TARGET_CHANNELS),
            '-acodec', 'pcm_s16le',
            str(temp_path)
        ]
        
        # Spuštění konverze
        result = subprocess.run(
            cmd,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Kontrola výstupu
        if result.returncode != 0 or not temp_path.exists():
            raise RuntimeError(f"FFmpeg error: {result.stderr}")
        
        # Nahrazení původního souboru
        temp_path.replace(output_path)
        input_path.unlink()
        
        return True
        
    except Exception as e:
        # Vyčištění po chybě
        if temp_path.exists():
            temp_path.unlink()
        
        logger.error(f"Chyba při konverzi {input_path}: {str(e)}")
        return False

def process_directory(root_dir: Path, logger: logging.Logger):
    """Zpracuje všechny MP3 soubory v adresáři a podadresářích"""
    if not root_dir.exists() or not root_dir.is_dir():
        logger.error(f"Neplatný adresář: {root_dir}")
        return 0, 0
    
    # Rekurzivní hledání MP3 souborů
    mp3_files = list(root_dir.rglob('*.mp3'))
    total_files = len(mp3_files)
    
    if not mp3_files:
        logger.info("Žádné MP3 soubory k převodu")
        return 0, 0
    
    logger.info(f"Načteno {total_files} MP3 souborů k převodu")
    
    # Paralelní zpracování s kontrolou prostředků
    max_workers = min(os.cpu_count() or 2, 8)  # Omezení pro stabilitu
    success_count = 0
    failed_count = 0
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {
            executor.submit(convert_file, mp3, logger): mp3
            for mp3 in mp3_files
        }
        
        for i, future in enumerate(as_completed(futures)):
            mp3_path = futures[future]
            try:
                if future.result():
                    success_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                logger.error(f"Kritická chyba u {mp3_path}: {str(e)}")
                failed_count += 1
            
            # Průběžný report
            if (i + 1) % 10 == 0 or (i + 1) == total_files:
                logger.info(
                    f"Průběh: {i+1}/{total_files} | "
                    f"Úspěšné: {success_count} | "
                    f"Chyby: {failed_count}"
                )
    
    return success_count, failed_count

def main():
    # Ověření FFmpeg
    if not check_ffmpeg():
        print("CHYBA: FFmpeg není nainstalován nebo není v PATH")
        print("Stáhněte z: https://ffmpeg.org/download.html")
        sys.exit(1)
    
    # Parsování argumentů
    parser = argparse.ArgumentParser(
        description='Převod MP3 na WAV (22kHz, 16-bit) s rekurzivním nahrazováním'
    )
    parser.add_argument(
        'directory',
        type=str,
        help='Kořenový adresář s MP3 soubory'
    )
    parser.add_argument(
        '--bit-depth',
        type=int,
        default=TARGET_BIT_DEPTH,
        choices=[16, 24, 32],
        help='Bitová hloubka výstupu (16, 24, 32)'
    )
    parser.add_argument(
        '--channels',
        type=int,
        default=TARGET_CHANNELS,
        choices=[1, 2],
        help='Počet kanálů (1=mono, 2=stereo)'
    )
    
    args = parser.parse_args()
    
    # Nastavení parametrů
    global TARGET_BIT_DEPTH, TARGET_CHANNELS
    TARGET_BIT_DEPTH = args.bit_depth
    TARGET_CHANNELS = args.channels
    
    # Inicializace loggeru
    logger = setup_logger()
    logger.info("=" * 60)
    logger.info(f"Spouštím převod MP3 -> WAV")
    logger.info(f"Adresář: {args.directory}")
    logger.info(f"Vzorkování: {TARGET_SAMPLE_RATE} Hz")
    logger.info(f"Bitová hloubka: {TARGET_BIT_DEPTH}-bit")
    logger.info(f"Kanály: {'mono' if TARGET_CHANNELS == 1 else 'stereo'}")
    logger.info("=" * 60)
    
    # Spuštění procesu
    root_dir = Path(args.directory).resolve()
    success, failed = process_directory(root_dir, logger)
    
    # Souhrnná zpráva
    logger.info("=" * 60)
    logger.info(f"PŘEVOD DOKONČEN: {success} úspěšných, {failed} chybných")
    logger.info(f"Podrobnosti v souboru: {os.path.abspath('conversion.log')}")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()